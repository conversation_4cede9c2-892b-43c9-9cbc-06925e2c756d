import React, {
  useRef,
  useState,
  useCallback,
  useEffect,
  useMemo,
} from "react";
import { throttle } from "lodash";
import CanvasToolbar from "./CanvasToolbar";
import CanvasGrid from "./CanvasGrid";
import {
  CANVAS_CONSTANTS,
  GRID_CONSTANTS,
  STICKY_NOTE_CONSTANTS,
} from "./CanvasConstants";
import StickyNote from "./StickyNote";
import { Button } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import "./InfiniteCanvas.css";

// 便签接口定义
interface StickyNoteData {
  id: string;
  position: {
    x: number;
    y: number;
  };
  content: string;
  color: string;
}

interface CanvasState {
  scale: number;
  offsetX: number;
  offsetY: number;
}

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  startOffsetX: number;
  startOffsetY: number;
}

const InfiniteCanvas: React.FC = () => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const requestRef = useRef<number | null>(null);
  const [canvasState, setCanvasState] = useState<CanvasState>({
    scale: CANVAS_CONSTANTS.DEFAULT_SCALE,
    offsetX: 0,
    offsetY: 0,
  });
  const [zoomAnimating, setZoomAnimating] = useState(false);

  // 便签状态
  const [stickyNotes, setStickyNotes] = useState<StickyNoteData[]>([]);

  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    startOffsetX: 0,
    startOffsetY: 0,
  });

  // 触发缩放动画
  const triggerZoomAnimation = useCallback(() => {
    setZoomAnimating(true);
    setTimeout(
      () => setZoomAnimating(false),
      CANVAS_CONSTANTS.ZOOM_ANIMATION_DURATION
    );
  }, []);

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // 添加新便签
  const addStickyNote = useCallback(() => {
    // 获取画布视口信息
    if (canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();

      // 计算视口中心点
      const viewportCenterX = rect.width / 2;
      const viewportCenterY = rect.height / 2;

      // 将视口坐标转换为画布坐标系中的坐标
      // 公式：画布坐标 = (视口坐标 - 画布偏移量) / 缩放比例
      // 这是因为 canvas-content 使用了 translate(offsetX, offsetY) scale(scale) 变换
      const canvasCenterX =
        (viewportCenterX - canvasState.offsetX) / canvasState.scale;
      const canvasCenterY =
        (viewportCenterY - canvasState.offsetY) / canvasState.scale;

      // 调试信息：输出坐标转换的详细信息
      console.log("便签生成坐标转换信息:", {
        视口中心: { x: viewportCenterX, y: viewportCenterY },
        画布偏移: { x: canvasState.offsetX, y: canvasState.offsetY },
        缩放比例: canvasState.scale,
        画布中心: { x: canvasCenterX, y: canvasCenterY },
        视口尺寸: { width: rect.width, height: rect.height },
      });

      // 便签的尺寸（在画布坐标系中）
      const stickyNoteWidth = STICKY_NOTE_CONSTANTS.WIDTH / canvasState.scale;
      const stickyNoteHeight = STICKY_NOTE_CONSTANTS.HEIGHT / canvasState.scale;

      // 计算视口在画布坐标系中的边界
      const viewportLeft = (0 - canvasState.offsetX) / canvasState.scale;
      const viewportTop = (0 - canvasState.offsetY) / canvasState.scale;
      const viewportRight =
        (rect.width - canvasState.offsetX) / canvasState.scale;
      const viewportBottom =
        (rect.height - canvasState.offsetY) / canvasState.scale;

      // 计算便签可以安全放置的区域（确保便签完全在视口内）
      const safeLeft = viewportLeft + stickyNoteWidth / 2;
      const safeTop = viewportTop + stickyNoteHeight / 2;
      const safeRight = viewportRight - stickyNoteWidth / 2;
      const safeBottom = viewportBottom - stickyNoteHeight / 2;

      // 在安全区域内生成便签位置
      let newX, newY;
      if (safeRight > safeLeft && safeBottom > safeTop) {
        // 安全区域足够大，在其中随机选择位置
        const randomX = Math.random() * (safeRight - safeLeft) + safeLeft;
        const randomY = Math.random() * (safeBottom - safeTop) + safeTop;

        // 添加小范围随机偏移以避免便签完全重叠
        const smallOffset = () =>
          (Math.random() * STICKY_NOTE_CONSTANTS.RANDOM_OFFSET_RANGE -
            STICKY_NOTE_CONSTANTS.RANDOM_OFFSET_RANGE / 2) /
          canvasState.scale;
        newX = randomX + smallOffset();
        newY = randomY + smallOffset();

        // 确保最终位置仍在安全区域内
        newX = Math.max(safeLeft, Math.min(safeRight, newX));
        newY = Math.max(safeTop, Math.min(safeBottom, newY));
      } else {
        // 安全区域太小，使用中心位置附近的小范围随机偏移
        const smallOffset = () =>
          (Math.random() * STICKY_NOTE_CONSTANTS.FALLBACK_OFFSET_RANGE -
            STICKY_NOTE_CONSTANTS.FALLBACK_OFFSET_RANGE / 2) /
          canvasState.scale;
        newX = canvasCenterX + smallOffset();
        newY = canvasCenterY + smallOffset();
      }

      // 随机选择便签颜色
      const colors = ["#f9ef8b", "#c3f0ca", "#f6cece", "#d7ecfb", "#e9dbf9"];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];

      const newNote: StickyNoteData = {
        id: generateId(),
        position: {
          // 使用计算后的画布坐标作为便签的初始位置
          // 这样便签将始终出现在当前视口的可见区域内
          x: newX,
          y: newY,
        },
        content: "在这里添加文本...",
        color: randomColor,
      };

      // 调试信息：输出最终的便签位置
      console.log("新便签创建:", {
        便签ID: newNote.id,
        便签位置: newNote.position,
        安全区域: {
          left: safeLeft,
          top: safeTop,
          right: safeRight,
          bottom: safeBottom,
        },
      });

      setStickyNotes((prevNotes) => [...prevNotes, newNote]);
    }
  }, [canvasState.offsetX, canvasState.offsetY, canvasState.scale]);

  // 删除便签
  const deleteStickyNote = useCallback((id: string) => {
    setStickyNotes((prevNotes) => prevNotes.filter((note) => note.id !== id));
  }, []);

  // 缩放功能
  const handleZoomIn = useCallback(() => {
    setCanvasState((prev) => ({
      ...prev,
      scale: Math.min(
        prev.scale * CANVAS_CONSTANTS.ZOOM_FACTOR,
        CANVAS_CONSTANTS.MAX_SCALE
      ),
    }));
    triggerZoomAnimation();
  }, [triggerZoomAnimation]);

  const handleZoomOut = useCallback(() => {
    setCanvasState((prev) => ({
      ...prev,
      scale: Math.max(
        prev.scale / CANVAS_CONSTANTS.ZOOM_FACTOR,
        CANVAS_CONSTANTS.MIN_SCALE
      ),
    }));
    triggerZoomAnimation();
  }, [triggerZoomAnimation]);

  // 重置画布
  const handleReset = useCallback(() => {
    setCanvasState({
      scale: CANVAS_CONSTANTS.DEFAULT_SCALE,
      offsetX: 0,
      offsetY: 0,
    });
    triggerZoomAnimation();
  }, [triggerZoomAnimation]);

  // 使用节流优化的滚轮缩放处理函数
  const handleWheelThrottled = useMemo(
    () =>
      throttle(
        (e: WheelEvent) => {
          e.preventDefault();

          // 优化：使用变量缓存频繁访问的值
          const currentScale = canvasState.scale;
          const delta = e.deltaY > 0 ? 0.9 : 1.1;
          const newScale = Math.min(
            Math.max(currentScale * delta, CANVAS_CONSTANTS.MIN_SCALE),
            CANVAS_CONSTANTS.MAX_SCALE
          );

          // 如果缩放比例没有变化，直接返回
          if (newScale === currentScale) return;

          if (canvasRef.current) {
            const rect = canvasRef.current.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            // 计算缩放中心点
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            // 调整偏移以保持鼠标位置不变
            const scaleRatio = newScale / canvasState.scale;
            const newOffsetX =
              canvasState.offsetX + (mouseX - centerX) * (1 - scaleRatio);
            const newOffsetY =
              canvasState.offsetY + (mouseY - centerY) * (1 - scaleRatio);

            setCanvasState({
              scale: newScale,
              offsetX: newOffsetX,
              offsetY: newOffsetY,
            });

            if (Math.abs(newScale - canvasState.scale) > 0.05) {
              triggerZoomAnimation();
            }
          }
        },
        CANVAS_CONSTANTS.WHEEL_THROTTLE_MS,
        { leading: true, trailing: true }
      ),
    [canvasState, triggerZoomAnimation]
  );

  // 使用 useEffect 清理节流函数
  useEffect(() => {
    return () => {
      handleWheelThrottled.cancel();
    };
  }, [handleWheelThrottled]);

  // 鼠标按下开始拖拽 - 使用React合成事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      setDragState({
        isDragging: true,
        startX: e.clientX,
        startY: e.clientY,
        startOffsetX: canvasState.offsetX,
        startOffsetY: canvasState.offsetY,
      });
    },
    [canvasState.offsetX, canvasState.offsetY]
  );

  // 使用 requestAnimationFrame 优化拖拽
  const updateDragPosition = useCallback(
    (clientX: number, clientY: number) => {
      if (dragState.isDragging) {
        const deltaX = clientX - dragState.startX;
        const deltaY = clientY - dragState.startY;

        setCanvasState((prev) => ({
          ...prev,
          offsetX: dragState.startOffsetX + deltaX,
          offsetY: dragState.startOffsetY + deltaY,
        }));

        requestRef.current = requestAnimationFrame(() =>
          updateDragPosition(clientX, clientY)
        );
      }
    },
    // 仅当拖拽状态相关值变化时才更新函数
    [
      dragState.isDragging,
      dragState.startX,
      dragState.startY,
      dragState.startOffsetX,
      dragState.startOffsetY,
    ]
  );

  // 优化后的鼠标移动处理函数
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (dragState.isDragging) {
        if (requestRef.current) {
          cancelAnimationFrame(requestRef.current);
        }
        requestRef.current = requestAnimationFrame(() =>
          updateDragPosition(e.clientX, e.clientY)
        );
      }
    },
    [dragState.isDragging, updateDragPosition]
  );

  // 鼠标松开结束拖拽
  const handleMouseUp = useCallback(() => {
    setDragState((prev) => ({ ...prev, isDragging: false }));
    if (requestRef.current) {
      cancelAnimationFrame(requestRef.current);
      requestRef.current = null;
    }
  }, []);

  // 添加事件监听器
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      // 使用React合成事件系统处理滚轮事件
      canvas.addEventListener("wheel", handleWheelThrottled, {
        passive: false,
      });
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      // 添加键盘快捷键支持
      const handleKeyDown = (e: KeyboardEvent) => {
        // 仅当没有输入框获得焦点时才处理快捷键
        if (
          !(
            e.target instanceof HTMLInputElement ||
            e.target instanceof HTMLTextAreaElement
          )
        ) {
          switch (e.key) {
            case "+":
            case "=": // 通常 = 和 + 在同一个键位
              if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                handleZoomIn();
              }
              break;
            case "-":
              if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                handleZoomOut();
              }
              break;
            case "0":
              if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                handleReset();
              }
              break;
            // 可以添加更多快捷键
          }
        }
      };

      // 移除双击重置功能
      // canvas.addEventListener("dblclick", handleReset);

      document.addEventListener("keydown", handleKeyDown);

      return () => {
        canvas.removeEventListener("wheel", handleWheelThrottled);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        // 移除双击重置功能的事件监听器清理
        // canvas.removeEventListener("dblclick", handleReset);
        document.removeEventListener("keydown", handleKeyDown);
        if (requestRef.current) {
          cancelAnimationFrame(requestRef.current);
          requestRef.current = null;
        }
      };
    }
  }, [
    handleWheelThrottled,
    handleMouseMove,
    handleMouseUp,
    handleZoomIn,
    handleZoomOut,
    handleReset,
  ]);

  // 计算一些性能关键参数，虽然我们已经移至CSS变量，但保留此逻辑以备未来使用
  // 并且可以用于某些需要JavaScript直接访问这些值的场景
  const _computedStyles = useMemo(() => {
    return {
      // 转换为像素值，便于JavaScript使用
      smallGridSizePx: GRID_CONSTANTS.SMALL_GRID_SIZE * canvasState.scale,
      largeGridSizePx: GRID_CONSTANTS.LARGE_GRID_SIZE * canvasState.scale,
      // 当前缩放比例的视口像素比
      devicePixelRatio: window.devicePixelRatio || 1,
      // 屏幕上可见的网格数量（近似值）
      visibleGridCellsX: canvasRef.current
        ? Math.ceil(
            canvasRef.current.clientWidth /
              (GRID_CONSTANTS.SMALL_GRID_SIZE * canvasState.scale)
          ) + 1
        : 0,
      visibleGridCellsY: canvasRef.current
        ? Math.ceil(
            canvasRef.current.clientHeight /
              (GRID_CONSTANTS.SMALL_GRID_SIZE * canvasState.scale)
          ) + 1
        : 0,
    };
  }, [canvasState.scale]); // 更新CSS变量 - 此处将JS状态同步到CSS变量
  useEffect(() => {
    if (canvasRef.current) {
      const container = canvasRef.current.parentElement;
      if (container) {
        // 优化：批量更新样式属性，减少重排
        const style = container.style;
        const { scale, offsetX, offsetY } = canvasState;

        // 更新主要变换变量
        style.setProperty("--canvas-scale", `${scale}`);

        // 计算网格位置偏移，使其能够正确对齐
        const smallGridSize = GRID_CONSTANTS.SMALL_GRID_SIZE * scale;
        const smallGridOffsetX = (offsetX % smallGridSize) + "px";
        const smallGridOffsetY = (offsetY % smallGridSize) + "px";

        // 设置基础偏移变量 - 对于简化版本，我们使用相同的偏移值
        style.setProperty("--canvas-offset-x", smallGridOffsetX);
        style.setProperty("--canvas-offset-y", smallGridOffsetY);

        // 设置内容偏移变量 (这是新增的，用于内容元素的变换)
        style.setProperty("--content-offset-x", `${offsetX}px`);
        style.setProperty("--content-offset-y", `${offsetY}px`);

        // 使用计算好的网格大小(仅做示例，实际上我们使用常量)
        console.log(
          "当前视口中预计可见网格单元数:",
          _computedStyles.visibleGridCellsX,
          _computedStyles.visibleGridCellsY
        );

        // 更新网格常量 - 理论上这些只需设置一次，但放在这里确保一致性
        container.style.setProperty(
          "--small-grid-size",
          `${GRID_CONSTANTS.SMALL_GRID_SIZE}px`
        );
        container.style.setProperty(
          "--large-grid-size",
          `${GRID_CONSTANTS.LARGE_GRID_SIZE}px`
        );
        container.style.setProperty(
          "--small-grid-color",
          GRID_CONSTANTS.SMALL_GRID_COLOR
        );
        container.style.setProperty(
          "--large-grid-color",
          GRID_CONSTANTS.LARGE_GRID_COLOR
        );
      }
    }
  }, [canvasState.scale, canvasState.offsetX, canvasState.offsetY]);

  return (
    <div className="infinite-canvas-container">
      {/* 使用拆分出的工具栏组件 */}
      <CanvasToolbar
        scale={canvasState.scale}
        zoomAnimating={zoomAnimating}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onReset={handleReset}
        minScale={CANVAS_CONSTANTS.MIN_SCALE}
        maxScale={CANVAS_CONSTANTS.MAX_SCALE}
      />

      {/* 画布区域 */}
      <div
        ref={canvasRef}
        className="infinite-canvas"
        onMouseDown={handleMouseDown}
      >
        {/* 使用拆分出的网格组件 - 不再传递样式参数，而是使用CSS变量 */}
        <CanvasGrid showAxis={false} />

        {/*
          内容区域 - 使用CSS变量控制变换，不再需要内联样式
          注意：当有特殊需求时可以使用内联样式覆盖CSS变量
        */}
        <div className="canvas-content">
          {/* 渲染所有便签 */}
          {stickyNotes.map((note) => (
            <StickyNote
              key={note.id}
              id={note.id}
              initialPosition={note.position}
              content={note.content}
              color={note.color}
              onDelete={deleteStickyNote}
            />
          ))}
        </div>
      </div>

      {/* 添加便签按钮 */}
      <div className="add-sticky-note-button">
        <Button
          type="primary"
          shape="circle"
          icon={<PlusOutlined />}
          size="large"
          onClick={addStickyNote}
          title="添加便签"
        />
      </div>
    </div>
  );
};

export default InfiniteCanvas;
