.infinite-canvas-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f8fafc; /* 更柔和的背景色 */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;

  /* 使用CSS变量存储关键值，便于JS访问和修改 */
  --canvas-scale: 1;
  --canvas-offset-x: 0px;
  --canvas-offset-y: 0px;
  --small-grid-size: 10px;
  --large-grid-size: 50px;
  --small-grid-color: rgba(226, 232, 240, 0.5);
  --large-grid-color: rgba(203, 213, 225, 0.5);

  /* 性能优化 - 避免重绘和重排 */
  will-change: transform;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.canvas-toolbar {
  position: fixed; /* 相对于视口定位 */
  top: 20px; /* 距离顶部一段距离 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%); /* 精确水平居中 */
  z-index: 100;
  padding: 12px 24px; /* 调整内边距以适应药丸形状 */
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50px; /* 创建药丸形状 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
  display: flex; /* 用于内部元素对齐 */
  align-items: center; /* 垂直对齐内部元素 */
  flex-direction: column; /* 新增：允许提示文本在下方 */

  /* 性能优化 */
  will-change: transform;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* 新增：小提示文本样式 */
.canvas-tooltip-help {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 6px;
  white-space: nowrap;
}

.zoom-indicator {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  margin-left: 12px;
  padding: 3px 8px;
  background-color: #f3f4f6;
  border-radius: 6px;
}

.infinite-canvas {
  width: 100%;
  height: 100%;
  cursor: grab;
  position: relative;
  background-color: #fafcff;
  transition: background-color 0.3s ease;

  /* 性能优化 */
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  /* 添加 GPU 加速 */
  perspective: 1000px;
  -webkit-perspective: 1000px;
  -webkit-backface-visibility: hidden;
}

.infinite-canvas:active {
  cursor: grabbing;
}

.canvas-content {
  width: 100%;
  height: 100%;
  transform-origin: 0 0;
  position: relative;
  will-change: transform; /* 优化性能 */

  /* 注意：我们现在需要额外添加位置变量来控制内容偏移，因为网格偏移变量已重新用途 */
  transform: translate3d(
      var(--content-offset-x, 0px),
      var(--content-offset-y, 0px),
      0
    )
    scale(var(--canvas-scale, 1));
}

/* 网格自定义样式 */
.grid-light {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  will-change: background;
}

/* 使用CSS变量的小网格 */
.grid-light.small-grid {
  background-image: linear-gradient(
      var(--small-grid-color) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, var(--small-grid-color) 1px, transparent 1px);
  background-size: calc(var(--small-grid-size) * var(--canvas-scale))
    calc(var(--small-grid-size) * var(--canvas-scale));
  background-position: var(--canvas-offset-x) var(--canvas-offset-y);
}

/* 使用CSS变量的大网格 */
.grid-light.large-grid {
  background-image: linear-gradient(
      var(--large-grid-color) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, var(--large-grid-color) 1px, transparent 1px);
  background-size: calc(var(--large-grid-size) * var(--canvas-scale))
    calc(var(--large-grid-size) * var(--canvas-scale));
  background-position: var(--canvas-offset-x) var(--canvas-offset-y);
}

/* 添加轴线，显示中心点 */
.canvas-axis {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.canvas-axis::before,
.canvas-axis::after {
  content: "";
  position: absolute;
  background-color: rgba(99, 102, 241, 0.1);
  z-index: 2;
}

.canvas-axis::before {
  width: 2px;
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.canvas-axis::after {
  width: 100%;
  height: 2px;
  top: 50%;
  transform: translateY(-50%);
}

/* 添加画布阴影效果 */
.canvas-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 40px rgba(51, 65, 85, 0.04);
  pointer-events: none;
  z-index: 5;
  border-radius: 2px;
}

/* 添加过渡效果，但注意避免对频繁变化的属性使用 */
.canvas-toolbar button {
  transition: background-color 0.2s ease, color 0.2s ease,
    border-color 0.2s ease;
}

/* 禁用文本选择 */
.infinite-canvas-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 滚动条隐藏 */
.infinite-canvas::-webkit-scrollbar {
  display: none;
}

.infinite-canvas {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 工具栏按钮美化 */
.canvas-toolbar .ant-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-toolbar .ant-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #1677ff;
}

.canvas-toolbar .ant-btn:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.canvas-toolbar .ant-btn[disabled] {
  color: rgba(0, 0, 0, 0.25);
  background-color: transparent;
}

/* 添加便签按钮样式 */
.add-sticky-note-button {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}

.add-sticky-note-button button {
  width: 56px;
  height: 56px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.add-sticky-note-button button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

/* 缩放指示器呼吸效果 */
@keyframes subtle-pulse {
  0% {
    background-color: #f3f4f6;
  }
  50% {
    background-color: #eef1f5;
  }
  100% {
    background-color: #f3f4f6;
  }
}

.zoom-change .zoom-indicator {
  animation: subtle-pulse 1.5s ease;
}

/* 便签容器样式 - 新方案：绝对定位，不受画布变换影响 */
.sticky-notes-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 让鼠标事件穿透到画布 */
  z-index: 10; /* 确保便签在画布内容之上 */
}

/* 便签本身需要重新启用鼠标事件 */
.sticky-notes-container .sticky-note {
  pointer-events: auto;
}
