.sticky-note {
  position: relative;
  width: 300px; /* 增加宽度与设计图更接近 */
  min-height: 300px; /* 增加高度与设计图更接近 */
  background-color: #fffde7; /* 淡黄色背景 */
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  border-radius: 8px; /* 更大的圆角 */
  padding: 0; /* 移除内边距，使头部边缘对齐 */
  display: flex;
  flex-direction: column;
  z-index: 100;
  cursor: default;
  overflow: hidden; /* 确保内容不会超出圆角 */
  /* 确保元素在其自身的图层上渲染 */
  transform-origin: center center;
  backface-visibility: hidden;
  /* 告诉浏览器这个元素会被频繁变换 */
  will-change: transform;
  /* 启用子像素渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 使用像素对齐 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.sticky-note-header {
  display: flex;
  justify-content: space-between; /* 标题在左侧，按钮在右侧 */
  align-items: center;
  padding: 10px 15px;
  cursor: move; /* 指示可拖动 */
  background-color: rgba(0, 0, 0, 0.03); /* 非常轻微的背景色 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  /* 防止文本模糊 */
  transform: translateZ(0);
  backface-visibility: hidden;
}

.sticky-note-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.7);
  /* 优化文本渲染 */
  text-rendering: optimizeLegibility;
}

.sticky-note-controls {
  display: flex;
}

.sticky-note-delete {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.4);
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 优化按钮文本渲染 */
  -webkit-font-smoothing: antialiased;
  text-rendering: geometricPrecision;
}

.sticky-note-delete:hover {
  color: rgba(0, 0, 0, 0.6);
}

.sticky-note-content {
  flex-grow: 1;
  padding: 15px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.8);
  overflow-wrap: break-word;
  word-wrap: break-word;
  /* 优化文本渲染 */
  text-rendering: optimizeLegibility;
  /* 开启子像素抗锯齿 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 防止内容区域大小变化 */
  box-sizing: border-box;
  line-height: 1.5;
}

.sticky-note-content > div {
  /* 确保预览状态的文本容器和 textarea 尺寸一致 */
  min-height: 230px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  white-space: pre-wrap;
}

.sticky-note-content textarea {
  width: 100%;
  height: 100%;
  min-height: 230px;
  background: transparent;
  border: none;
  resize: none;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  outline: none;
  padding: 0;
  margin: 0;
  /* 优化文本域的渲染 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 确保盒模型计算一致 */
  box-sizing: border-box;
  line-height: inherit;
  display: block;
  white-space: pre-wrap;
}

/* 悬停效果 */
.sticky-note:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
