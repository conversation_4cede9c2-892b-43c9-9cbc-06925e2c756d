import React, { useState, useRef } from "react";
import Draggable, { type DraggableEvent } from "react-draggable";
import "./StickyNote.css";

export interface StickyNoteProps {
  id: string;
  initialPosition: { x: number; y: number };
  content: string;
  color?: string;
  onDelete?: (id: string) => void;
}

const StickyNote: React.FC<StickyNoteProps> = ({
  id,
  initialPosition,
  content,
  color = "#fff9c4", // 更新为更淡的黄色，接近设计图
  onDelete,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [noteContent, setNoteContent] = useState(content);
  const nodeRef = useRef<HTMLDivElement>(null);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNoteContent(e.target.value);
  };

  const handleContentBlur = () => {
    setIsEditing(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(id);
    }
  };

  // 新增拖动开始事件处理器
  const handleDragStart = (e: DraggableEvent) => {
    // 确保事件存在 stopPropagation 方法 (对于 MouseEvent/TouchEvent 应该存在)
    if (typeof (e as Event).stopPropagation === "function") {
      (e as Event).stopPropagation();
    }
  };

  return (
    <Draggable
      defaultPosition={initialPosition}
      handle=".sticky-note-header"
      nodeRef={nodeRef}
      onStart={handleDragStart}
    >
      <div
        ref={nodeRef}
        className="sticky-note"
        style={{ backgroundColor: color }}
      >
        <div className="sticky-note-header">
          <div className="sticky-note-title">便签</div>
          <button className="sticky-note-delete" onClick={handleDelete}>
            ×
          </button>
        </div>
        <div className="sticky-note-content" onClick={handleEdit}>
          {isEditing ? (
            <textarea
              value={noteContent}
              onChange={handleContentChange}
              onBlur={handleContentBlur}
              autoFocus
            />
          ) : (
            <div>{noteContent}</div>
          )}
        </div>
      </div>
    </Draggable>
  );
};

export default StickyNote;
